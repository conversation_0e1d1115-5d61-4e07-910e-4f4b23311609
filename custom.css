/* Fleet IDE & macOS-inspired VS Code UI - Minimalistic & Professional */

/* ===== GLOBAL STYLES ===== */
:root {
    --border-radius-small: 6px;
    --border-radius-medium: 10px;
    --border-radius-large: 14px;
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 12px;
    --spacing-lg: 16px;
    --glass-bg: rgba(255, 255, 255, 0.05);
    --glass-border: rgba(255, 255, 255, 0.08);
    --shadow-subtle: 0 2px 8px rgba(0, 0, 0, 0.15);
    --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.2);
    --transition-smooth: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Remove default borders and add smooth transitions */
* {
    transition: var(--transition-smooth) !important;
}

/* ===== WORKBENCH LAYOUT ===== */
.monaco-workbench {
    background: transparent !important;
}

.monaco-workbench>.part {
    border: none !important;
    margin: var(--spacing-xs) !important;
}

/* ===== ACTIVITY BAR ===== */
.activitybar {
    background: var(--glass-bg) !important;
    backdrop-filter: blur(20px) saturate(180%) !important;
    border: 1px solid var(--glass-border) !important;
    border-radius: var(--border-radius-large) !important;
    margin: var(--spacing-sm) var(--spacing-xs) var(--spacing-sm) var(--spacing-sm) !important;
    box-shadow: var(--shadow-subtle) !important;
    width: 52px !important;
}

.activitybar .monaco-action-bar .action-item {
    margin: var(--spacing-xs) 0 !important;
}

.activitybar .monaco-action-bar .action-item .action-label {
    border-radius: var(--border-radius-small) !important;
    margin: 2px !important;
    transition: var(--transition-smooth) !important;
}

.activitybar .monaco-action-bar .action-item .action-label:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    transform: scale(1.05) !important;
}

.activitybar .monaco-action-bar .action-item.checked .action-label {
    background: rgba(255, 255, 255, 0.15) !important;
    box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.2) !important;
}

/* ===== SIDEBAR ===== */
.sidebar {
    background: var(--glass-bg) !important;
    backdrop-filter: blur(20px) saturate(180%) !important;
    border: 1px solid var(--glass-border) !important;
    border-radius: var(--border-radius-large) !important;
    margin: var(--spacing-sm) var(--spacing-xs) var(--spacing-sm) 0 !important;
    box-shadow: var(--shadow-subtle) !important;
}

.sidebar .composite.title {
    background: transparent !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.06) !important;
    padding: var(--spacing-md) var(--spacing-lg) !important;
}

.sidebar .composite.title .title-label {
    font-weight: 600 !important;
    font-size: 13px !important;
    letter-spacing: 0.3px !important;
}

/* ===== EDITOR AREA ===== */
.editor-group-container {
    background: var(--glass-bg) !important;
    backdrop-filter: blur(20px) saturate(180%) !important;
    border: 1px solid var(--glass-border) !important;
    border-radius: var(--border-radius-large) !important;
    margin: var(--spacing-sm) !important;
    box-shadow: var(--shadow-medium) !important;
    overflow: hidden !important;
}

/* ===== TABS ===== */
.tabs-container {
    background: transparent !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.06) !important;
    padding: var(--spacing-xs) var(--spacing-sm) 0 !important;
}

.tab {
    background: transparent !important;
    border: none !important;
    border-radius: var(--border-radius-small) var(--border-radius-small) 0 0 !important;
    margin: 0 2px !important;
    padding: var(--spacing-sm) var(--spacing-md) !important;
    min-height: 36px !important;
    position: relative !important;
    overflow: hidden !important;
}

.tab:hover {
    background: rgba(255, 255, 255, 0.08) !important;
}

.tab.active {
    background: rgba(255, 255, 255, 0.12) !important;
    box-shadow: inset 0 -2px 0 currentColor !important;
}

.tab .label-name {
    font-weight: 500 !important;
    font-size: 12px !important;
    letter-spacing: 0.2px !important;
}

/* ===== PANEL (Terminal, Output, etc.) ===== */
.panel {
    background: var(--glass-bg) !important;
    backdrop-filter: blur(20px) saturate(180%) !important;
    border: 1px solid var(--glass-border) !important;
    border-radius: var(--border-radius-large) !important;
    margin: 0 var(--spacing-sm) var(--spacing-sm) var(--spacing-sm) !important;
    box-shadow: var(--shadow-subtle) !important;
}

.panel .composite.title {
    background: transparent !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.06) !important;
    padding: var(--spacing-sm) var(--spacing-lg) !important;
}

/* ===== STATUS BAR ===== */
.statusbar {
    background: var(--glass-bg) !important;
    backdrop-filter: blur(20px) saturate(180%) !important;
    border: 1px solid var(--glass-border) !important;
    border-radius: var(--border-radius-medium) !important;
    margin: 0 var(--spacing-sm) var(--spacing-sm) var(--spacing-sm) !important;
    box-shadow: var(--shadow-subtle) !important;
    height: 28px !important;
    font-size: 11px !important;
    font-weight: 500 !important;
}

.statusbar-item {
    padding: 0 var(--spacing-sm) !important;
    border-radius: var(--border-radius-small) !important;
    margin: 2px !important;
}

.statusbar-item:hover {
    background: rgba(255, 255, 255, 0.1) !important;
}

/* ===== ENHANCED COMPONENTS ===== */

/* File Explorer Tree */
.monaco-tree .monaco-tree-row {
    border-radius: var(--border-radius-small) !important;
    margin: 1px var(--spacing-xs) !important;
    transition: var(--transition-smooth) !important;
}

.monaco-tree .monaco-tree-row:hover {
    background: rgba(255, 255, 255, 0.08) !important;
}

.monaco-tree .monaco-tree-row.selected {
    background: rgba(255, 255, 255, 0.12) !important;
    box-shadow: inset 2px 0 0 currentColor !important;
}

/* Command Palette */
.quick-input-widget {
    background: var(--glass-bg) !important;
    backdrop-filter: blur(30px) saturate(180%) !important;
    border: 1px solid var(--glass-border) !important;
    border-radius: var(--border-radius-large) !important;
    box-shadow: var(--shadow-medium) !important;
    overflow: hidden !important;
}

.quick-input-header {
    background: transparent !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.06) !important;
    padding: var(--spacing-md) !important;
}

.quick-input-list .monaco-list-row {
    border-radius: var(--border-radius-small) !important;
    margin: 2px var(--spacing-sm) !important;
    transition: var(--transition-smooth) !important;
}

.quick-input-list .monaco-list-row:hover {
    background: rgba(255, 255, 255, 0.08) !important;
}

.quick-input-list .monaco-list-row.focused {
    background: rgba(255, 255, 255, 0.12) !important;
}

/* Scrollbars */
.monaco-scrollable-element>.scrollbar {
    background: transparent !important;
}

.monaco-scrollable-element>.scrollbar>.slider {
    background: rgba(255, 255, 255, 0.2) !important;
    border-radius: 6px !important;
    transition: var(--transition-smooth) !important;
}

.monaco-scrollable-element>.scrollbar>.slider:hover {
    background: rgba(255, 255, 255, 0.3) !important;
}

/* Context Menus */
.context-view .monaco-menu {
    background: var(--glass-bg) !important;
    backdrop-filter: blur(30px) saturate(180%) !important;
    border: 1px solid var(--glass-border) !important;
    border-radius: var(--border-radius-medium) !important;
    box-shadow: var(--shadow-medium) !important;
    padding: var(--spacing-xs) !important;
}

.context-view .monaco-menu .monaco-action-bar .action-item {
    border-radius: var(--border-radius-small) !important;
    margin: 1px !important;
    transition: var(--transition-smooth) !important;
}

.context-view .monaco-menu .monaco-action-bar .action-item:hover {
    background: rgba(255, 255, 255, 0.1) !important;
}

/* Notifications */
.notifications-toasts {
    margin: var(--spacing-lg) !important;
}

.monaco-workbench .notifications-list-container .notification-toast {
    background: var(--glass-bg) !important;
    backdrop-filter: blur(30px) saturate(180%) !important;
    border: 1px solid var(--glass-border) !important;
    border-radius: var(--border-radius-large) !important;
    box-shadow: var(--shadow-medium) !important;
    margin-bottom: var(--spacing-sm) !important;
}

/* Minimap */
.minimap {
    border-radius: var(--border-radius-small) !important;
    overflow: hidden !important;
}

/* Breadcrumbs */
.monaco-breadcrumbs {
    background: transparent !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.06) !important;
    padding: var(--spacing-sm) var(--spacing-lg) !important;
}

.monaco-breadcrumb-item {
    border-radius: var(--border-radius-small) !important;
    padding: 2px var(--spacing-xs) !important;
    transition: var(--transition-smooth) !important;
}

.monaco-breadcrumb-item:hover {
    background: rgba(255, 255, 255, 0.08) !important;
}

/* Search Results */
.search-view .search-widget {
    background: transparent !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.06) !important;
    padding: var(--spacing-md) !important;
}

/* Terminal Enhancements */
.terminal-outer-container {
    padding: var(--spacing-sm) !important;
}

.xterm-screen {
    border-radius: var(--border-radius-small) !important;
    overflow: hidden !important;
}

/* Settings UI */
.settings-editor .settings-header {
    background: transparent !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.06) !important;
    padding: var(--spacing-lg) !important;
}

.settings-editor .setting-item {
    border-radius: var(--border-radius-small) !important;
    margin: var(--spacing-xs) 0 !important;
    padding: var(--spacing-md) !important;
    transition: var(--transition-smooth) !important;
}

.settings-editor .setting-item:hover {
    background: rgba(255, 255, 255, 0.05) !important;
}