/* Modernize VS Code UI with macOS-inspired aesthetics */

/* Primary and Secondary Sidebar: Rounded corners and distinct background */
.side-bar, .secondary-side-bar {
    border-radius: 12px !important;
    background-color: rgba(30, 30, 30, 0.95) !important; /* Darker, semi-transparent background */
    margin: 8px !important; /* Gap around sidebars */
    backdrop-filter: blur(10px); /* macOS vibrancy effect */
    border: 1px solid rgba(255, 255, 255, 0.1); /* Subtle border */
}

/* Activity Bar: Adjust to complement sidebars */
.activity-bar {
    background-color: transparent !important;
    margin-right: 8px; /* Gap between activity bar and sidebar */
}

/* Editor Tabs: Rounded corners */
.monaco-workbench .part.editor > .content .editor-group-container > .title .tabs .tab {
    border-radius: 8px !important;
    margin: 0 4px !important; /* Gap between tabs */
    background-color: rgba(40, 40, 40, 0.95) !important; /* Slightly lighter than sidebar */
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    transition: background-color 0.2s ease; /* Smooth hover effect */
}

/* Active Tab Styling */
.monaco-workbench .part.editor > .content .editor-group-container > .title .tabs .tab.active {
    background-color: rgba(80CBC4, 0.2) !important; /* Tie to your theme’s accent color */
    border-bottom: 2px solid #80CBC4 !important;
}

/* Editor Area: Add padding for visual separation */
.editor-container {
    padding: 8px !important;
    background-color: rgba(20, 20, 20, 0.95) !important; /* Slightly darker main background */
}

/* Panel (bottom area, e.g., terminal): Rounded and spaced */
.panel {
    border-radius: 12px !important;
    margin: 8px !important;
    background-color: rgba(30, 30, 30, 0.95) !important;
    backdrop-filter: blur(10px);
}

/* General Component Spacing */
.monaco-workbench > .part {
    margin: 4px !important; /* Subtle gap between major components */
}

/* Remove unnecessary borders for cleaner look */
.monaco-workbench .part > .content {
    border: none !important;
}

/* Enhance hover effects for interactivity */
.monaco-workbench .part.editor > .content .editor-group-container > .title .tabs .tab:hover {
    background-color: rgba(80CBC4, 0.3) !important;
}

/* Adjust status bar (if re-enabled) */
.statusbar {
    border-radius: 8px !important;
    margin: 4px 8px !important;
}