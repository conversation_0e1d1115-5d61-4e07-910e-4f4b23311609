/* Fleet IDE & macOS-inspired VS Code UI - Minimalistic & Professional */

/* ===== DESIGN SYSTEM ===== */
:root {
    /* Border Radius */
    --radius-xs: 4px;
    --radius-sm: 6px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;

    /* Spacing */
    --space-xs: 2px;
    --space-sm: 4px;
    --space-md: 8px;
    --space-lg: 12px;
    --space-xl: 16px;
    --space-2xl: 20px;
    --space-3xl: 24px;

    /* Glass Morphism */
    --glass-bg-primary: rgba(255, 255, 255, 0.06);
    --glass-bg-secondary: rgba(255, 255, 255, 0.04);
    --glass-bg-tertiary: rgba(255, 255, 255, 0.02);
    --glass-border-primary: rgba(255, 255, 255, 0.12);
    --glass-border-secondary: rgba(255, 255, 255, 0.08);
    --glass-border-tertiary: rgba(255, 255, 255, 0.04);

    /* Interactive States */
    --hover-bg: rgba(255, 255, 255, 0.08);
    --active-bg: rgba(255, 255, 255, 0.12);
    --selected-bg: rgba(255, 255, 255, 0.15);
    --focus-bg: rgba(255, 255, 255, 0.1);

    /* Shadows */
    --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.1);
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.12);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.18);
    --shadow-xl: 0 12px 24px rgba(0, 0, 0, 0.2);

    /* Backdrop Filters */
    --blur-sm: blur(8px) saturate(150%);
    --blur-md: blur(16px) saturate(160%);
    --blur-lg: blur(24px) saturate(170%);
    --blur-xl: blur(32px) saturate(180%);

    /* Transitions */
    --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: all 0.35s cubic-bezier(0.4, 0, 0.2, 1);

    /* Typography */
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --letter-spacing-tight: -0.025em;
    --letter-spacing-normal: 0;
    --letter-spacing-wide: 0.025em;
}

/* Global Smooth Transitions */
* {
    transition: var(--transition-normal) !important;
}

/* Remove Default Borders */
.monaco-workbench .part>.content,
.monaco-workbench .part.editor>.content,
.monaco-workbench .part.sidebar>.content,
.monaco-workbench .part.panel>.content {
    border: none !important;
}

/* ===== WORKBENCH LAYOUT ===== */
.monaco-workbench {
    background: transparent !important;
    gap: var(--space-md) !important;
}

.monaco-workbench>.part {
    border: none !important;
    margin: var(--space-sm) !important;
}

/* ===== ACTIVITY BAR ===== */
.activitybar {
    background: var(--glass-bg-primary) !important;
    backdrop-filter: var(--blur-lg) !important;
    border: 1px solid var(--glass-border-primary) !important;
    border-radius: var(--radius-xl) !important;
    margin: var(--space-lg) var(--space-sm) var(--space-lg) var(--space-lg) !important;
    box-shadow: var(--shadow-md) !important;
    width: 56px !important;
    padding: var(--space-md) 0 !important;
}

/* Activity Bar Items */
.activitybar .monaco-action-bar {
    padding: 0 var(--space-sm) !important;
}

.activitybar .monaco-action-bar .action-item {
    margin: var(--space-sm) 0 !important;
    border-radius: var(--radius-md) !important;
    overflow: hidden !important;
}

.activitybar .monaco-action-bar .action-item .action-label {
    border-radius: var(--radius-md) !important;
    margin: var(--space-xs) !important;
    padding: var(--space-md) !important;
    transition: var(--transition-fast) !important;
    position: relative !important;
    overflow: hidden !important;
}

.activitybar .monaco-action-bar .action-item .action-label:hover {
    background: var(--hover-bg) !important;
    transform: scale(1.02) !important;
    box-shadow: var(--shadow-sm) !important;
}

.activitybar .monaco-action-bar .action-item.checked .action-label {
    background: var(--selected-bg) !important;
    box-shadow: inset 0 0 0 1px var(--glass-border-primary), var(--shadow-sm) !important;
}

.activitybar .monaco-action-bar .action-item.checked .action-label::before {
    content: '';
    position: absolute !important;
    left: -2px !important;
    top: 20% !important;
    bottom: 20% !important;
    width: 3px !important;
    
    background: currentColor !important;
    border-radius: 0 2px 2px 0 !important;
    opacity: 0.8 !important;
}

/* Activity Bar Badge */
.activitybar .monaco-action-bar .action-item .badge {
    border-radius: var(--radius-sm) !important;
    font-size: 10px !important;
    font-weight: var(--font-weight-semibold) !important;
    padding: 2px 6px !important;
    min-width: 16px !important;
    height: 16px !important;
}

/* ===== SIDEBAR ===== */
.sidebar {
    background: var(--glass-bg-primary) !important;
    backdrop-filter: var(--blur-lg) !important;
    border: 1px solid var(--glass-border-primary) !important;
    border-radius: var(--radius-xl) !important;
    margin: var(--space-lg) var(--space-sm) var(--space-lg) 0 !important;
    box-shadow: var(--shadow-md) !important;
    overflow: hidden !important;
}

/* Sidebar Header */
.sidebar .composite.title {
    background: var(--glass-bg-secondary) !important;
    border-bottom: 1px solid var(--glass-border-tertiary) !important;
    padding: var(--space-xl) var(--space-2xl) !important;
    backdrop-filter: var(--blur-sm) !important;
}

.sidebar .composite.title .title-label {
    font-weight: var(--font-weight-semibold) !important;
    font-size: 13px !important;
    letter-spacing: var(--letter-spacing-wide) !important;
    text-transform: uppercase !important;
    opacity: 0.9 !important;
}

.sidebar .composite.title .title-actions {
    gap: var(--space-sm) !important;
}

.sidebar .composite.title .title-actions .action-label {
    border-radius: var(--radius-sm) !important;
    padding: var(--space-sm) !important;
    transition: var(--transition-fast) !important;
}

.sidebar .composite.title .title-actions .action-label:hover {
    background: var(--hover-bg) !important;
}

/* Sidebar Content */
.sidebar .content {
    padding: var(--space-md) 0 !important;
}

/* File Explorer */
.explorer-folders-view {
    padding: 0 var(--space-md) !important;
}

.monaco-tree .monaco-tree-row {
    border-radius: var(--radius-md) !important;
    margin: var(--space-xs) var(--space-md) !important;
    padding: var(--space-sm) var(--space-md) !important;
    transition: var(--transition-fast) !important;
    position: relative !important;
}

.monaco-tree .monaco-tree-row:hover {
    background: var(--hover-bg) !important;
}

.monaco-tree .monaco-tree-row.selected {
    background: var(--selected-bg) !important;
    box-shadow: inset 3px 0 0 currentColor !important;
}

.monaco-tree .monaco-tree-row.focused {
    background: var(--focus-bg) !important;
    outline: 1px solid var(--glass-border-secondary) !important;
    outline-offset: -1px !important;
}

/* Tree Icons */
.monaco-tree .monaco-tree-row .monaco-icon-label .monaco-icon-label-container {
    gap: var(--space-md) !important;
}

/* Folder/File Names */
.monaco-tree .monaco-tree-row .monaco-icon-label .monaco-icon-label-container .monaco-icon-name-container {
    font-weight: var(--font-weight-medium) !important;
    font-size: 13px !important;
    letter-spacing: var(--letter-spacing-normal) !important;
}

/* ===== EDITOR AREA ===== */
.editor-group-container {
    background: var(--glass-bg-primary) !important;
    backdrop-filter: var(--blur-lg) !important;
    border: 1px solid var(--glass-border-primary) !important;
    border-radius: var(--radius-xl) !important;
    margin: var(--space-lg) !important;
    box-shadow: var(--shadow-lg) !important;
    overflow: hidden !important;
}

.editor-group-container .editor-container {
    border-radius: 0 0 var(--radius-xl) var(--radius-xl) !important;
    overflow: hidden !important;
}

/* ===== TABS ===== */
.tabs-container {
    background: var(--glass-bg-secondary) !important;
    backdrop-filter: var(--blur-sm) !important;
    border-bottom: 1px solid var(--glass-border-tertiary) !important;
    padding: var(--space-md) var(--space-lg) 0 !important;
    min-height: 48px !important;
}

.tabs-and-actions-container {
    gap: var(--space-md) !important;
}

/* Individual Tabs */
.tab {
    background: transparent !important;
    border: none !important;
    border-radius: var(--radius-md) var(--radius-md) 0 0 !important;
    margin: 0 var(--space-xs) !important;
    padding: var(--space-md) var(--space-xl) !important;
    min-height: 40px !important;
    position: relative !important;
    overflow: hidden !important;
    transition: var(--transition-fast) !important;
}

.tab:hover {
    background: var(--hover-bg) !important;
    transform: translateY(-1px) !important;
}

.tab.active {
    background: var(--active-bg) !important;
    box-shadow: inset 0 -3px 0 currentColor, var(--shadow-sm) !important;
    transform: translateY(-2px) !important;
}

.tab.active::before {
    content: '';
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    height: 2px !important;
    background: currentColor !important;
    opacity: 0.6 !important;
}

/* Tab Content */
.tab .label-name {
    font-weight: var(--font-weight-medium) !important;
    font-size: 13px !important;
    letter-spacing: var(--letter-spacing-normal) !important;
}

.tab .label-description {
    font-size: 11px !important;
    opacity: 0.7 !important;
    margin-left: var(--space-sm) !important;
}

/* Tab Close Button */
.tab .action-label.codicon-close {
    border-radius: var(--radius-sm) !important;
    padding: var(--space-xs) !important;
    margin-left: var(--space-md) !important;
    transition: var(--transition-fast) !important;
    opacity: 0.6 !important;
}

.tab .action-label.codicon-close:hover {
    background: var(--hover-bg) !important;
    opacity: 1 !important;
}

/* Tab Actions */
.editor-actions {
    padding: var(--space-md) var(--space-lg) !important;
    gap: var(--space-sm) !important;
}

.editor-actions .action-label {
    border-radius: var(--radius-sm) !important;
    padding: var(--space-sm) !important;
    transition: var(--transition-fast) !important;
}

.editor-actions .action-label:hover {
    background: var(--hover-bg) !important;
}

/* ===== PANEL (Terminal, Output, etc.) ===== */
.panel {
    background: var(--glass-bg-primary) !important;
    backdrop-filter: var(--blur-lg) !important;
    border: 1px solid var(--glass-border-primary) !important;
    border-radius: var(--radius-xl) !important;
    margin: 0 var(--space-lg) var(--space-lg) var(--space-lg) !important;
    box-shadow: var(--shadow-md) !important;
    overflow: hidden !important;
}

.panel .composite.title {
    background: var(--glass-bg-secondary) !important;
    backdrop-filter: var(--blur-sm) !important;
    border-bottom: 1px solid var(--glass-border-tertiary) !important;
    padding: var(--space-lg) var(--space-2xl) !important;
}

.panel .composite.title .title-label {
    font-weight: var(--font-weight-semibold) !important;
    font-size: 13px !important;
    letter-spacing: var(--letter-spacing-wide) !important;
    text-transform: uppercase !important;
    opacity: 0.9 !important;
}

.panel .content {
    padding: var(--space-lg) !important;
}

/* ===== STATUS BAR ===== */
.statusbar {
    background: var(--glass-bg-primary) !important;
    backdrop-filter: var(--blur-lg) !important;
    border: 1px solid var(--glass-border-primary) !important;
    border-radius: var(--radius-lg) !important;
    margin: 0 var(--space-lg) var(--space-lg) var(--space-lg) !important;
    box-shadow: var(--shadow-md) !important;
    height: 32px !important;
    font-size: 12px !important;
    font-weight: var(--font-weight-medium) !important;
    padding: 0 var(--space-md) !important;
}

.statusbar-item {
    padding: var(--space-sm) var(--space-md) !important;
    border-radius: var(--radius-sm) !important;
    margin: var(--space-xs) !important;
    transition: var(--transition-fast) !important;
    height: 24px !important;
    display: flex !important;
    align-items: center !important;
}

.statusbar-item:hover {
    background: var(--hover-bg) !important;
    transform: scale(1.02) !important;
}

.statusbar-item.has-beak {
    border-radius: var(--radius-sm) !important;
}

/* Status Bar Sections */
.statusbar-left,
.statusbar-right {
    gap: var(--space-xs) !important;
}

/* ===== ENHANCED COMPONENTS ===== */

/* Command Palette & Quick Input */
.quick-input-widget {
    background: var(--glass-bg-primary) !important;
    backdrop-filter: var(--blur-xl) !important;
    border: 1px solid var(--glass-border-primary) !important;
    border-radius: var(--radius-xl) !important;
    box-shadow: var(--shadow-xl) !important;
    overflow: hidden !important;
    margin: var(--space-3xl) !important;
}

.quick-input-header {
    background: var(--glass-bg-secondary) !important;
    backdrop-filter: var(--blur-sm) !important;
    border-bottom: 1px solid var(--glass-border-tertiary) !important;
    padding: var(--space-xl) var(--space-2xl) !important;
}

.quick-input-filter {
    padding: 0 var(--space-2xl) !important;
}

.quick-input-box {
    border-radius: var(--radius-lg) !important;
    padding: var(--space-lg) var(--space-xl) !important;
    font-size: 14px !important;
    font-weight: var(--font-weight-medium) !important;
    background: var(--glass-bg-tertiary) !important;
    border: 1px solid var(--glass-border-secondary) !important;
    transition: var(--transition-normal) !important;
}

.quick-input-box:focus {
    border-color: var(--glass-border-primary) !important;
    box-shadow: 0 0 0 2px var(--glass-border-secondary) !important;
}

.quick-input-list {
    padding: var(--space-md) !important;
}

.quick-input-list .monaco-list-row {
    border-radius: var(--radius-md) !important;
    margin: var(--space-xs) var(--space-md) !important;
    padding: var(--space-md) var(--space-lg) !important;
    transition: var(--transition-fast) !important;
}

.quick-input-list .monaco-list-row:hover {
    background: var(--hover-bg) !important;
}

.quick-input-list .monaco-list-row.focused {
    background: var(--focus-bg) !important;
    outline: 1px solid var(--glass-border-secondary) !important;
    outline-offset: -1px !important;
}

.quick-input-list .monaco-list-row .monaco-icon-label {
    font-weight: var(--font-weight-medium) !important;
}

.quick-input-list .monaco-list-row .monaco-icon-label .label-description {
    opacity: 0.7 !important;
    font-size: 12px !important;
}

/* Scrollbars */
.monaco-scrollable-element>.scrollbar {
    background: transparent !important;
    width: 8px !important;
    height: 8px !important;
}

.monaco-scrollable-element>.scrollbar>.slider {
    background: var(--glass-border-secondary) !important;
    border-radius: var(--radius-sm) !important;
    transition: var(--transition-fast) !important;
    min-width: 6px !important;
    min-height: 6px !important;
}

.monaco-scrollable-element>.scrollbar>.slider:hover {
    background: var(--glass-border-primary) !important;
}

.monaco-scrollable-element>.scrollbar>.slider:active {
    background: var(--hover-bg) !important;
}

/* Context Menus */
.context-view .monaco-menu {
    background: var(--glass-bg-primary) !important;
    backdrop-filter: var(--blur-xl) !important;
    border: 1px solid var(--glass-border-primary) !important;
    border-radius: var(--radius-lg) !important;
    box-shadow: var(--shadow-lg) !important;
    padding: var(--space-md) !important;
    overflow: hidden !important;
}

.context-view .monaco-menu .monaco-action-bar .action-item {
    border-radius: var(--radius-md) !important;
    margin: var(--space-xs) !important;
    transition: var(--transition-fast) !important;
    overflow: hidden !important;
}

.context-view .monaco-menu .monaco-action-bar .action-item .action-label {
    padding: var(--space-md) var(--space-lg) !important;
    font-weight: var(--font-weight-medium) !important;
    font-size: 13px !important;
}

.context-view .monaco-menu .monaco-action-bar .action-item:hover {
    background: var(--hover-bg) !important;
}

.context-view .monaco-menu .monaco-action-bar .action-item.focused {
    background: var(--focus-bg) !important;
}

/* Notifications */
.notifications-toasts {
    margin: var(--space-2xl) !important;
    gap: var(--space-md) !important;
}

.monaco-workbench .notifications-list-container .notification-toast {
    background: var(--glass-bg-primary) !important;
    backdrop-filter: var(--blur-xl) !important;
    border: 1px solid var(--glass-border-primary) !important;
    border-radius: var(--radius-xl) !important;
    box-shadow: var(--shadow-lg) !important;
    margin-bottom: var(--space-lg) !important;
    padding: var(--space-xl) !important;
    overflow: hidden !important;
}

.notification-toast .notification-toast-contents {
    gap: var(--space-lg) !important;
}

/* Minimap */
.minimap {
    border-radius: var(--radius-md) !important;
    overflow: hidden !important;
    margin: var(--space-md) !important;
    box-shadow: var(--shadow-sm) !important;
}

/* Breadcrumbs */
.monaco-breadcrumbs {
    background: var(--glass-bg-tertiary) !important;
    backdrop-filter: var(--blur-sm) !important;
    border-bottom: 1px solid var(--glass-border-tertiary) !important;
    padding: var(--space-lg) var(--space-2xl) !important;
    font-size: 12px !important;
}

.monaco-breadcrumb-item {
    border-radius: var(--radius-sm) !important;
    padding: var(--space-sm) var(--space-md) !important;
    transition: var(--transition-fast) !important;
    font-weight: var(--font-weight-medium) !important;
}

.monaco-breadcrumb-item:hover {
    background: var(--hover-bg) !important;
}

.monaco-breadcrumb-item.focused {
    background: var(--focus-bg) !important;
    outline: 1px solid var(--glass-border-secondary) !important;
    outline-offset: -1px !important;
}

/* Search & Replace */
.search-view .search-widget {
    background: var(--glass-bg-secondary) !important;
    backdrop-filter: var(--blur-sm) !important;
    border-bottom: 1px solid var(--glass-border-tertiary) !important;
    padding: var(--space-xl) !important;
    border-radius: var(--radius-lg) var(--radius-lg) 0 0 !important;
}

.search-view .search-widget .monaco-inputbox {
    border-radius: var(--radius-md) !important;
    overflow: hidden !important;
}

.search-view .search-widget .monaco-inputbox .ibwrapper {
    background: var(--glass-bg-tertiary) !important;
    border: 1px solid var(--glass-border-secondary) !important;
    border-radius: var(--radius-md) !important;
}

/* Terminal */
.terminal-outer-container {
    padding: var(--space-lg) !important;
    border-radius: 0 0 var(--radius-xl) var(--radius-xl) !important;
    overflow: hidden !important;
}

.xterm-screen {
    border-radius: var(--radius-md) !important;
    overflow: hidden !important;
    background: var(--glass-bg-tertiary) !important;
    padding: var(--space-lg) !important;
}

/* Settings UI */
.settings-editor .settings-header {
    background: var(--glass-bg-secondary) !important;
    backdrop-filter: var(--blur-sm) !important;
    border-bottom: 1px solid var(--glass-border-tertiary) !important;
    padding: var(--space-2xl) !important;
}

.settings-editor .settings-header .settings-header-controls {
    gap: var(--space-md) !important;
}

.settings-editor .setting-item {
    border-radius: var(--radius-md) !important;
    margin: var(--space-md) 0 !important;
    padding: var(--space-xl) !important;
    transition: var(--transition-fast) !important;
    border: 1px solid transparent !important;
}

.settings-editor .setting-item:hover {
    background: var(--hover-bg) !important;
    border-color: var(--glass-border-tertiary) !important;
}

.settings-editor .setting-item.is-configured {
    background: var(--glass-bg-tertiary) !important;
    border-color: var(--glass-border-secondary) !important;
}

/* Input Fields */
.monaco-inputbox .ibwrapper {
    border-radius: var(--radius-md) !important;
    background: var(--glass-bg-tertiary) !important;
    border: 1px solid var(--glass-border-secondary) !important;
    transition: var(--transition-fast) !important;
}

.monaco-inputbox .ibwrapper:focus-within {
    border-color: var(--glass-border-primary) !important;
    box-shadow: 0 0 0 2px var(--glass-border-secondary) !important;
}

/* Buttons */
.monaco-button {
    border-radius: var(--radius-md) !important;
    padding: var(--space-md) var(--space-xl) !important;
    font-weight: var(--font-weight-medium) !important;
    transition: var(--transition-fast) !important;
    border: 1px solid var(--glass-border-secondary) !important;
}

.monaco-button:hover {
    transform: translateY(-1px) !important;
    box-shadow: var(--shadow-sm) !important;
}

.monaco-button.secondary {
    background: var(--glass-bg-secondary) !important;
    backdrop-filter: var(--blur-sm) !important;
}

/* Dropdown */
.monaco-dropdown {
    border-radius: var(--radius-md) !important;
    overflow: hidden !important;
}

.monaco-dropdown .dropdown-label {
    padding: var(--space-md) var(--space-lg) !important;
    background: var(--glass-bg-tertiary) !important;
    border: 1px solid var(--glass-border-secondary) !important;
    border-radius: var(--radius-md) !important;
    transition: var(--transition-fast) !important;
}

.monaco-dropdown .dropdown-label:hover {
    background: var(--hover-bg) !important;
}